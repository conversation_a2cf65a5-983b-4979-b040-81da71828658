{"hosting": [{"site": "elevar-pos-staging", "public": "dist/apps/pos-client/browser", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/**", "destination": "https://elevar-develop.frappe.cloud/api/:splat"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "/**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*-sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|js|css|eot|otf|ttf|ttc|woff|woff2|font.css", "headers": [{"key": "Cache-Control", "value": "max-age=604800"}]}]}, {"site": "elevar-pos", "public": "dist/apps/pos-client/browser", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*-sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|js|css|eot|otf|ttf|ttc|woff|woff2|font.css", "headers": [{"key": "Cache-Control", "value": "max-age=604800"}]}]}], "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}]}