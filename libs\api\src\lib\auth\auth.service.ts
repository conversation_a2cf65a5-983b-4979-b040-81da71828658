import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { catchError, lastValueFrom, map, Observable, of, switchMap, tap } from 'rxjs';
import { Employee, LoginRequest, LoginResponse, User } from '@models';
import { EmployeeService } from '../employee/employee.service';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService extends BaseHttpService<unknown> {
  private readonly _employeeService = inject(EmployeeService);
  private readonly _cookieService = inject(CookieService);
  private readonly _router = inject(Router);

  private readonly _authBaseUrl = 'https://elevar-develop.frappe.cloud/api/method';

  isLoggedIn: WritableSignal<boolean> = signal(false);
  loggedInUser: WritableSignal<User | null> = signal(null);

  constructor() {
    super();
    this.initializeService('login', this._authBaseUrl);



    // Load user data from localStorage only once
    const storedUser = localStorage.getItem('current-user');
    console.log('🏗️ Constructor: Initial current-user from localStorage:', storedUser);

    if (storedUser) {
      try {
        this.loggedInUser.set(JSON.parse(storedUser));
        console.log('🏗️ Constructor: Loaded user from localStorage:', JSON.parse(storedUser));
      } catch (e) {
        console.error('🏗️ Constructor: Error parsing stored user', e);
        localStorage.removeItem('current-user');
      }
    }

    const loggedInEmployee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');
    console.log('🏗️ Constructor: Initial employee from localStorage:', loggedInEmployee);

    if (!!loggedInEmployee && !!this.loggedInUser()) {
      this.isLoggedIn.set(true);
      console.log('🏗️ Constructor: User is logged in');
    } else {
      this.isLoggedIn.set(false);
      console.log('🏗️ Constructor: User is not logged in');
    }

    effect(() => {
      if (this.isLoggedIn()) {
        this.setEmployeeDetails(this.loggedInUser()?.user_id as string);
      }
    });
  }

  // Override getHeaders for login-specific content type
  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  login(credentials: LoginRequest): Observable<Employee | null> {
    // Clear existing data
    this.clearAuthData();

    const formData = new URLSearchParams();
    formData.set('usr', credentials.usr);
    formData.set('pwd', credentials.pwd);

    return this.http.post<LoginResponse>(
      this.apiUrl,
      formData.toString(),
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      switchMap(res => {
        if (res.message === 'Logged In') {
          // Create user object from login response
          const user: User = {
            user_id: credentials.usr,
            full_name: res.full_name || 'User',
            system_user: 'yes',
            user_image: ''
          };

          // Store in localStorage
          localStorage.setItem('current-user', JSON.stringify(user));
          this.loggedInUser.set(user);

          console.log('✅ Login successful, getting employee details...');

          // Make employee request in the same HTTP context as login
          // This should inherit the session from the login request
          return this.getEmployeeDetailsWithSession(user.user_id);
        }
        return of(null);
      }),
      tap(employee => {
        if (employee) {
          this.isLoggedIn.set(true);
          console.log('✅ Login successful, employee found:', employee);
        } else {
          console.log('❌ Login failed or no employee found');
        }
      }),
      catchError(error => {
        console.error('🚨 Login error:', error);
        return of(null);
      })
    );
  }

  async setEmployeeDetails(id: string) {
    return await lastValueFrom(this.getEmployeeDetails(id));
  }

  logout(): Observable<unknown> {
    return this.http.get(
      `${this.apiUrl}/logout`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(() => {
        this.clearAuthData();
        this._router.navigate(['/auth/login']);
      })
    );
  }

  getEmployeeDetails(user_id: string) {
    console.log('🍪 Checking cookies before employee API call...');

    // Check what cookies are available
    const allCookies = this._cookieService.getAll();
    console.log('🍪 All available cookies:', allCookies);
    console.log('🍪 Number of cookies:', Object.keys(allCookies).length);

    // Check specific ERPNext cookies
    const sidCookie = this._cookieService.get('sid');
    const userIdCookie = this._cookieService.get('user_id');

    console.log('🍪 SID cookie:', sidCookie || 'NOT FOUND');
    console.log('🍪 User ID cookie:', userIdCookie || 'NOT FOUND');

    // Check document.cookie directly
    console.log('🍪 Document.cookie:', document.cookie);

    return this._employeeService.getEmployeeByUserId(user_id);
  }

  /**
   * Get employee details using the same HTTP client as login
   * This should inherit the session from the login request
   */
  private getEmployeeDetailsWithSession(user_id: string): Observable<Employee | null> {
    console.log('🔄 Making employee request with login session context...');

    const filters = [["user_id", "=", user_id]];
    const fields = ["name", "employee_name", "user_id", "company", "employee_number", "designation", "branch"];

    const requestParams = {
      filters: JSON.stringify(filters),
      fields: JSON.stringify(fields)
    };

    // Use the same HTTP client that was used for login
    return this.http.get<any>(
      'https://elevar-develop.frappe.cloud/api/resource/Employee',
      {
        params: requestParams,
        headers: new HttpHeaders({
          'Content-Type': 'application/json'
        }),
        withCredentials: true
      }
    ).pipe(
      map(res => {
        if (res.data && res.data.length > 0) {
          const employee = res.data[0];
          console.log('🔄 ✅ Employee found with session context');

          // Store employee data in localStorage
          localStorage.setItem('loggeed-in-employee', JSON.stringify(employee));
          this._employeeService.loggedInEmployee.set(employee);
          return employee;
        }

        console.log('🔄 ❌ No employee found for user_id:', user_id);
        return null;
      }),
      catchError(err => {
        console.error('🔄 🚨 Employee API error with session context:', err.status, err.message);

        // If this fails too, fall back to the employee service
        console.log('🔄 Falling back to employee service...');
        return this._employeeService.getEmployeeByUserId(user_id);
      })
    );
  }



  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    console.log('🧹 clearAuthData called');
    console.trace('🧹 clearAuthData stack trace:');

    // Clear localStorage
    console.log('🧹 Removing current-user from localStorage');
    localStorage.removeItem('current-user');
    console.log('🧹 Removing loggeed-in-employee from localStorage');
    localStorage.removeItem('loggeed-in-employee');

    // Clear cookies more thoroughly
    const domain = 'elevar-develop.frappe.cloud';
    const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];

    console.log('🧹 Clearing cookies:', cookieNames);
    cookieNames.forEach(name => {
      this._cookieService.delete(name, '/');
      this._cookieService.delete(name, '/', domain);
      this._cookieService.delete(name, '/', `.${domain}`);
      // Also try without domain
      this._cookieService.delete(name);
    });

    // Reset signals
    console.log('🧹 Resetting auth signals');
    this.isLoggedIn.set(false);
    this.loggedInUser.set(null);

    console.log('🧹 clearAuthData completed');
  }


}
