import { effect, inject, Injectable, isDevMode, signal, WritableSignal } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { catchError, lastValueFrom, Observable, of, switchMap, tap } from 'rxjs';
import { Employee, LoginRequest, LoginResponse, User } from '@models';
import { EmployeeService } from '../employee/employee.service';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService extends BaseHttpService<unknown> {
  private readonly _employeeService = inject(EmployeeService);
  private readonly _cookieService = inject(CookieService);
  private readonly _router = inject(Router);

  private readonly _authBaseUrl = 'https://elevar-develop.frappe.cloud/api/method';

  isLoggedIn: WritableSignal<boolean> = signal(false);
  loggedInUser: WritableSignal<User | null> = signal(null);

  constructor() {
    super();
    this.initializeService('login', this._authBaseUrl);
    
    // Load user data from localStorage only once
    const storedUser = localStorage.getItem('current-user');
    if (storedUser) {
      try {
        this.loggedInUser.set(JSON.parse(storedUser));
      } catch (e) {
        console.error('Error parsing stored user', e);
        localStorage.removeItem('current-user');
      }
    }
    
    const loggedInEmployee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');
    
    if (!!loggedInEmployee && !!this.loggedInUser()) {
      this.isLoggedIn.set(true);
    } else {
      this.isLoggedIn.set(false);
    }

    effect(() => {
      if (this.isLoggedIn()) {
        this.setEmployeeDetails(this.loggedInUser()?.user_id as string);
      }
    });
  }

  // Override getHeaders for login-specific content type
  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  login(credentials: LoginRequest): Observable<Employee | null> {
    console.log('🚀🚀🚀 AUTH SERVICE LOGIN METHOD CALLED 🚀🚀🚀');
    console.log('🚀 Login method called with credentials:', { usr: credentials.usr });
    console.log('🚀 API URL:', this.apiUrl);
    console.log('🚀 this.http exists:', !!this.http);

    console.log('🚀 Creating form data...');
    const formData = new URLSearchParams();
    formData.set('usr', credentials.usr);
    formData.set('pwd', credentials.pwd);

    console.log('🚀 Form data prepared:', formData.toString());

    // Clear existing data
    console.log('🚀 Clearing auth data...');
    try {
      this.clearAuthData();
      console.log('🚀 Auth data cleared successfully');
    } catch (error) {
      console.error('🚨 Error clearing auth data:', error);
    }

    console.log('🚀 Making HTTP POST request...');

    console.log('🚀 About to make HTTP POST with:', {
      url: this.apiUrl,
      body: formData.toString(),
      headers: this.getHeaders(),
      withCredentials: true
    });

    return this.http.post<LoginResponse>(
      this.apiUrl,
      formData.toString(),
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(res => {
        console.log('🚀 HTTP POST completed successfully');
        console.log('🚀 Login response:', res);
      }),
      switchMap(res => {
        console.log('🚀 Processing login response in switchMap:', res);

        if (res.message === 'Logged In') {
          console.log('🚀 Login message confirmed, creating user from login response...');

          // Since cross-domain cookies don't work, create user object from login response
          const user: User = {
            user_id: credentials.usr, // Use the login username as user_id
            full_name: res.full_name || 'User', // Use full_name from login response
            system_user: 'yes', // Assume system user since they can log in
            user_image: '' // No image available from login response
          };

          console.log('🚀 Created user object from login response:', user);

          // Store in localStorage for persistence
          localStorage.setItem('current-user', JSON.stringify(user));
          console.log('🚀 User stored in localStorage');
          console.log('🚀 Verifying localStorage:', localStorage.getItem('current-user'));

          this.loggedInUser.set(user);
          console.log('🚀 User successfully set in loggedInUser signal');
          console.log('🚀 Getting employee details for user_id:', user.user_id);
          console.log('🚀 About to call getEmployeeDetails...');

          return this.getEmployeeDetails(user.user_id);
        } else {
          console.log('🚀 Login message is not "Logged In", got:', res.message);
        }
        return of(null);
      }),
      tap(employee => {
        if (employee) {
          this.isLoggedIn.set(true);
          if (isDevMode()) {
            console.log('✅ Login successful, employee found:', employee);
          }
        } else {
          if (isDevMode()) {
            console.log('❌ Login failed or no employee found');
          }
        }
      }),
      catchError(error => {
        if (isDevMode()) {
          console.error('🚨 Login error:', error);
        }
        return of(null);
      })
    );
  }

  async setEmployeeDetails(id: string) {
    return await lastValueFrom(this.getEmployeeDetails(id));
  }

  logout(): Observable<unknown> {
    return this.http.get(
      `${this.apiUrl}/logout`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(() => {
        this.clearAuthData();
        this._router.navigate(['/auth/login']);
      })
    );
  }

  getEmployeeDetails(user_id: string) {
    console.log('🚀 getEmployeeDetails called with user_id:', user_id);
    console.log('🚀 Employee service exists:', !!this._employeeService);
    console.log('🚀 About to call _employeeService.getEmployeeByUserId...');
    return this._employeeService.getEmployeeByUserId(user_id);
  }



  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    // Clear localStorage
    localStorage.removeItem('current-user');
    localStorage.removeItem('loggeed-in-employee');

    // Clear cookies more thoroughly
    const domain = 'elevar-develop.frappe.cloud';
    const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];

    cookieNames.forEach(name => {
      this._cookieService.delete(name, '/');
      this._cookieService.delete(name, '/', domain);
      this._cookieService.delete(name, '/', `.${domain}`);
      // Also try without domain
      this._cookieService.delete(name);
    });

    // Reset signals
    this.isLoggedIn.set(false);
    this.loggedInUser.set(null);
  }


}
