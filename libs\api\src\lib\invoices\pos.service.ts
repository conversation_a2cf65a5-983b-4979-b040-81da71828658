import { inject, Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {
  BalanceDetail,
  FrappeListResponse,
  FrappeSingleResponse,
  PaymentReconciliation,
  POSInvoiceRequest,
  POSInvoiceResponse,
  PosOpeningEntry,
  POSOpeningEntryDetailResponse,
  PosOpeningEntryListRequest,
  POSTransaction,
} from '@models';
import { BaseHttpService, NotificationService } from '@core';
import { PosProfileService } from '../pos-profile/pos-profile.service';
import { AuthService } from '../auth/auth.service';
import { formatDate, formatSimpleDate } from '../auth/common/_datefns_';

@Injectable({
  providedIn: 'root'
})
export class PosService extends BaseHttpService<FrappeListResponse<PosOpeningEntry>> {
  private readonly _posProfileService = inject(PosProfileService);
  private readonly _authService = inject(AuthService);
  private readonly _notificationService = inject(NotificationService);

  constructor() {
    super();
    this.initializeService(
      'POS Opening Entry',
      '/api/resource'
    );
  }


  createOpeningEntry(balanceDetail: BalanceDetail[]): Observable<PosOpeningEntry> {
    const profile = this._posProfileService.posProfile()?.name;
    const company = this._posProfileService.posProfile()?.company;
    const user = this._authService.loggedInUser()?.user_id;
    const period_start_date = formatDate(new Date());
    const posting_date = formatSimpleDate(new Date());

    if (!profile || !company || !user) {
      const missing = [
        !profile && 'POS Profile',
        !company && 'Company',
        !user && 'User'
      ].filter(Boolean).join(', ');

      this._notificationService.setError('Error creating opening entry', `Required fields missing: ${missing}. Kindly contact Administrator.`)
      throw new Error(`Required fields missing: ${missing}`);
    }

    const entry = {
      data: {
        pos_profile: profile,
        company,
        user,
        period_start_date,
        posting_date,
        balance_details: balanceDetail,
        docstatus: 1,
      }
    };

    return this.http.post<PosOpeningEntry>(this.apiUrl, entry, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get POS Opening Entries with filters and fields
   */
  getOpeningEntries(request: PosOpeningEntryListRequest): Observable<FrappeListResponse<PosOpeningEntry>> {
    let params = new HttpParams();

    if (request.fields) {
      params = params.set('fields', JSON.stringify(request.fields));
    }

    if (request.filters) {
      params = params.set('filters', JSON.stringify(request.filters));
    }

    if (request.limit) {
      params = params.set('limit', request.limit.toString());
    }

    if (request.offset) {
      params = params.set('offset', request.offset.toString());
    }

    params = params.set('order_by', 'period_start_date desc');

    return this.http.get<FrappeListResponse<PosOpeningEntry>>(this.apiUrl, {
      headers: this.getHeaders(),
      params,
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a single POS Opening Entry by name
   * @param name The unique identifier of the POS Opening Entry
   * @returns Observable containing the POS Opening Entry details
   */
  getOpeningEntry(name: string): Observable<FrappeSingleResponse<POSOpeningEntryDetailResponse>> {
    const entryUrl = `${this.apiUrl}/${encodeURIComponent(name)}`;

    return this.http.get<FrappeSingleResponse<POSOpeningEntryDetailResponse>>(entryUrl, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Create a new POS Invoice
   */
  createPOSInvoice(invoice: POSInvoiceRequest): Observable<POSInvoiceResponse> {
    const url = this.apiUrl.replace('POS Opening Entry', '');
    return this.http.post<POSInvoiceResponse>(
      url + 'POS Invoice',
      invoice,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      catchError(this.handleError)
    );
  }

  /**
 * Create a new POS Closing Entry
 */
  createClosingEntry(
    openingEntry: string,
    paymentReconciliation: PaymentReconciliation[],
    posTransactions: POSTransaction[]
  ): Observable<any> {
    const profile = this._posProfileService.posProfile()?.name;
    const company = this._posProfileService.posProfile()?.company;
    const user = this._authService.loggedInUser()?.user_id;
    const period_end_date = formatDate(new Date());
    const posting_date = formatSimpleDate(new Date());

    if (!profile || !company || !user) {
      const missing = [
        !profile && 'POS Profile',
        !company && 'Company',
        !user && 'User'
      ].filter(Boolean).join(', ');
      this._notificationService.setError('Error creating closing entry', `Required fields missing: ${missing}. Kindly contact Administrator.`);
      throw new Error(`Required fields missing: ${missing}`);
    }

    const data = {
      period_end_date,
      posting_date,
      pos_opening_entry: openingEntry,
      company,
      pos_profile: profile,
      user,
      payment_reconciliation: paymentReconciliation,
      pos_transactions: posTransactions,
      docstatus: 1,
    };

    const closingEntryUrl = this.apiUrl.replace('POS Opening Entry', 'POS Closing Entry');
    const payload = { data };

    return this.http.post<any>(closingEntryUrl, payload, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }
}