import { inject, Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable, catchError, map, of, switchMap, tap } from 'rxjs';
import { BaseHttpService } from '@core';
import { FilterCondition, FilterOperator, FrappeListResponse, InvoiceDetail, POSCheckResponse, POSInvoice, POSInvoiceResponse, PosOpeningEntry, PosOpeningEntryListRequest, PosOpeningStatus } from '@models';
import { AuthService } from '../auth/auth.service';
import { PosService } from './pos.service';

@Injectable({
  providedIn: 'root'
})
export class InvoiceService extends BaseHttpService<POSInvoiceResponse> {
  private readonly _baseUrl = '';
  private readonly _authService = inject(AuthService);
  private readonly _posService = inject(PosService);

  constructor() {
    super();
    this.initializeService('api/resource/POS Invoice', this._baseUrl);
  }
  getInvoiceDetail(name: string): Observable<InvoiceDetail> {
    return this.http
      .get<{data: InvoiceDetail}>(
        `${this.apiUrl}/${name}`,
        {
          headers: this.getHeaders(),
          withCredentials: true
        }
      )
      .pipe(
        map(response => response.data),
        catchError(this.handleError)
      );
  }
  /**
   * Get all POS invoices with specified fields
   * @returns Observable<POSInvoice[]>
   */
  getAllInvoices(order?: 'asc' | 'desc'): Observable<POSInvoice[]> {
    const fields = [
      'currency',
      'customer',
      'discount_amount',
      'grand_total',
      'rounded_total',
      'name',
      'outstanding_amount',
      'paid_amount',
      'posting_date',
      'posting_time',
      'selling_price_list',
      'status',
      'is_return'
    ];

    const _order = order ?? 'desc';
    const params = new HttpParams()
      .set('fields', JSON.stringify(fields))
      .set('order_by', `posting_date ${_order}`);

    return this.http
      .get<POSInvoiceResponse>(this.apiUrl, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(map(response => response.data),
        catchError(this.handleError));
  }

  private _getOpeningEntriesOfUser(): Observable<FrappeListResponse<PosOpeningEntry> | null> {
    const loggedInUser = this._authService.loggedInUser();
    if (!loggedInUser) {
      return of(null);
    }

    // Define the fields you want to retrieve
    const fields: Array<keyof PosOpeningEntry> = ['name', 'status', 'period_start_date', 'pos_profile'];

    // Define the filters to apply
    const filters: FilterCondition[] = [
      ['user', FilterOperator.EQUALS, loggedInUser.user_id]
    ];

    // Construct the request object
    const request: PosOpeningEntryListRequest = {
      fields: fields,
      filters: filters
    };

    // Call the service method with the constructed request
    return this._posService.getOpeningEntries(request);
  }

  /**
  * Saves invoice check response data to session storage
  * @param response The POSCheckResponse object to save
  */
  private saveInvoiceCheckResponseToStorage(response: POSCheckResponse): void {
    sessionStorage.setItem('is-last-entry-closed', JSON.stringify(response.isLastEntryClosed));
    sessionStorage.setItem('has-opening-entry-today', JSON.stringify(response.hasOpeningEntryToday));
    sessionStorage.setItem('most-recent-opening-entry', JSON.stringify(response.mostRecentEntry));
  }

  /**
   * Checks if user can create an invoice and saves the results to session storage
   * @returns Observable<POSCheckResponse>
   */
  checkIfUserCanCreateInvoice(): Observable<POSCheckResponse> {
    return this._getOpeningEntriesOfUser().pipe(
      map((response) => {
        // Validate the response
        if (!response || !Array.isArray(response.data)) {
          const result = {
            isLastEntryClosed: false,
            hasOpeningEntryToday: false,
            mostRecentEntry: null
          };
          this.saveInvoiceCheckResponseToStorage(result);
          return result;
        }

        // Get today's date in YYYY-MM-DD format
        const today = new Date().toISOString().split('T')[0];

        // Filter entries for today
        const todayEntries = response.data.filter((entry) =>
          entry.period_start_date?.startsWith(today)
        );

        // Determine if there's an opening entry today
        const hasOpeningEntryToday = todayEntries.length > 0;

        // Sort by date descending to get the most recent entry
        const mostRecentEntry = response.data.toSorted((a, b) =>
          b.period_start_date.localeCompare(a.period_start_date)
        )[0];

        // Determine if the last entry is closed
        const isLastEntryClosed = mostRecentEntry?.status === PosOpeningStatus.CLOSED;

        const result = {
          isLastEntryClosed,
          hasOpeningEntryToday,
          mostRecentEntry
        };


        return result;
      }),
      switchMap(res => {
        let response = {
          isLastEntryClosed: res.isLastEntryClosed,
          hasOpeningEntryToday: res.hasOpeningEntryToday,
          mostRecentEntry: res.mostRecentEntry
        };

        if (res.mostRecentEntry) {
          return this._posService.getOpeningEntry(res.mostRecentEntry.name).pipe(map(res => {
            response = {
              ...response,
              mostRecentEntry: res.data
            }
            this.saveInvoiceCheckResponseToStorage(response);
            return response
          }))
        }
        this.saveInvoiceCheckResponseToStorage(response);

        return of(response);

      }),
      catchError((error) => {
        const fallbackResult = {
          isLastEntryClosed: false,
          hasOpeningEntryToday: false,
          mostRecentEntry: null
        };
        this.saveInvoiceCheckResponseToStorage(fallbackResult);
        console.log(error);
        return of(fallbackResult);
      })
    );
  }

  /**
   * Submit an invoice by setting docstatus to 1
   */
  submitInvoice(name: string): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/${name}`,
      { docstatus: 1 },
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(catchError(this.handleError));
  }
}