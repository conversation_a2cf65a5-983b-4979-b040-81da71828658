import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateChildFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { CookieService } from 'ngx-cookie-service';

export const isLoggedInGuard: CanActivateChildFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const cookieService = inject(CookieService);

  console.log('🛡️ isLoggedInGuard: Checking authentication status...');

  // Check for user data in localStorage
  const currentUser = JSON.parse(localStorage.getItem('current-user') ?? 'null');
  const employee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');

  console.log('🛡️ Current user from localStorage:', currentUser);
  console.log('🛡️ Employee from localStorage:', employee);

  // Since we're using cross-domain authentication, we only check localStorage
  // The sid cookie is set on elevar-develop.frappe.cloud and not accessible from elevar-pos-staging.web.app
  const isLoggedIn = !!currentUser && !!employee;

  console.log('🛡️ Is logged in:', isLoggedIn);

  if (!isLoggedIn) {
    console.log('🛡️ User not authenticated, redirecting to login...');
    router.navigate(['/auth/login']);
    localStorage.clear();
    sessionStorage.clear();
    cookieService.deleteAll('/');
    return false;
  }

  console.log('🛡️ User authenticated, allowing access');
  return true;
};


