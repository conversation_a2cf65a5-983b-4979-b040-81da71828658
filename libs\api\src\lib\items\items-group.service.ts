import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseHttpService } from "@core";
import { ItemGroup, ItemGroupResponse } from "@models";
import { catchError, map, Observable } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ItemGroupService extends BaseHttpService<ItemGroupResponse> {
  private readonly _baseUrl = '';

  constructor() {
    super();
    this.initializeService('api/resource/Item Group', this._baseUrl);
  }

  getSubgroups(parent_item_group: string): Observable<ItemGroup[]> {
    const params = new HttpParams()
      .set('filters', JSON.stringify([["parent_item_group", "=", parent_item_group]]));

    return this.http
      .get<ItemGroupResponse>(this.apiUrl, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(
        map(response => response.data),
        catchError(this.handleError)
      );
  }
}