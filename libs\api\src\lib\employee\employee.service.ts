import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { Observable, lastValueFrom, map, tap, catchError, of } from 'rxjs';
import { Employee, EmployeeResponse } from '@models';
import { PosProfileService } from '../pos-profile/pos-profile.service';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService extends BaseHttpService<Employee> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/Employee';
  private readonly _posProfileService = inject(PosProfileService);

  loggedInEmployee: WritableSignal<Employee | null> = signal(null);

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
    this.loggedInEmployee.set(JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null'));
    effect(() => {
      if (this.loggedInEmployee()) {
        this.setPOSProfile(this.loggedInEmployee() as Employee);
      }
    })

  }

  getEmployeeByUserId(userId: string): Observable<Employee | null> {
    console.log('👤👤👤 GET EMPLOYEE BY USER ID CALLED 👤👤👤');
    console.log('👤 Searching for employee with user_id:', userId);
    console.log('👤 Base URL:', this._baseUrl);
    console.log('👤 Full API URL:', `${this._baseUrl}/api/resource/Employee`);

    const filters = [["user_id", "=", userId]];
    const fields = ["name", "employee_name", "user_id", "company", "employee_number", "designation", "branch"];

    console.log('👤 Request filters:', filters);
    console.log('👤 Request fields:', fields);
    console.log('👤 Filters JSON:', JSON.stringify(filters));
    console.log('👤 Fields JSON:', JSON.stringify(fields));

    const requestParams = {
      filters: JSON.stringify(filters),
      fields: JSON.stringify(fields)
    };

    console.log('👤 Request params object:', requestParams);
    console.log('👤 WithCredentials:', true);
    console.log('👤 HTTP service exists:', !!this.http);

    return this.http.get<any>(
      `${this._baseUrl}/api/resource/Employee`,
      {
        params: requestParams,
        withCredentials: true
      }
    ).pipe(
      tap(res => {
        console.log('👤 Raw HTTP response received:', res);
        console.log('👤 Response type:', typeof res);
        console.log('👤 Response keys:', Object.keys(res || {}));
      }),
      map(res => {
        console.log('👤 Processing response in map operator...');
        console.log('👤 Employee API response:', res);
        console.log('👤 Response data exists:', !!res.data);
        console.log('👤 Response data type:', typeof res.data);
        console.log('👤 Response data length:', res.data ? res.data.length : 'N/A');

        if (res.data && res.data.length > 0) {
          const employee = res.data[0];
          console.log('👤 ✅ Found employee:', employee);
          console.log('👤 Employee keys:', Object.keys(employee || {}));

          // Store employee data in localStorage
          console.log('👤 Storing employee in localStorage...');
          localStorage.setItem('loggeed-in-employee', JSON.stringify(employee));
          console.log('👤 Employee stored in localStorage successfully');
          console.log('👤 Verifying localStorage:', localStorage.getItem('loggeed-in-employee'));

          this.loggedInEmployee.set(employee);
          console.log('👤 Employee signal updated');
          return employee;
        }

        console.log('👤 ❌ No employee found for user_id:', userId);
        console.log('👤 Response data was:', res.data);
        return null;
      }),
      catchError(err => {
        console.error('👤 🚨 Error fetching employee:', err);
        console.error('👤 🚨 Error status:', err.status);
        console.error('👤 🚨 Error message:', err.message);
        console.error('👤 🚨 Error details:', err.error);
        return of(null);
      })
    );
  }

  async setPOSProfile(employee: Employee) {
    return await lastValueFrom(this._posProfileService.getPOSProfileForEmployeeInBranch(employee))
  }
}
