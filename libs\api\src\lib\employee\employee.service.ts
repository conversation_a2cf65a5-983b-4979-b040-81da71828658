import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { Observable, lastValueFrom, map, catchError, of } from 'rxjs';
import { Employee, EmployeeResponse } from '@models';
import { PosProfileService } from '../pos-profile/pos-profile.service';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService extends BaseHttpService<Employee> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/Employee';
  private readonly _posProfileService = inject(PosProfileService);

  loggedInEmployee: WritableSignal<Employee | null> = signal(null);

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
    this.loggedInEmployee.set(JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null'));
    effect(() => {
      if (this.loggedInEmployee()) {
        this.setPOSProfile(this.loggedInEmployee() as Employee);
      }
    })

  }

  getEmployeeByUserId(userId: string): Observable<Employee | null> {
    console.log('👤 Fetching employee for user_id:', userId);

    const filters = [["user_id", "=", userId]];
    const fields = ["name", "employee_name", "user_id", "company", "employee_number", "designation", "branch"];

    const requestParams = {
      filters: JSON.stringify(filters),
      fields: JSON.stringify(fields)
    };

    return this.http.get<any>(
      `${this._baseUrl}/api/resource/Employee`,
      {
        params: requestParams,
        withCredentials: true
      }
    ).pipe(
      map(res => {
        if (res.data && res.data.length > 0) {
          const employee = res.data[0];
          console.log('👤 ✅ Employee found and stored in localStorage');

          // Store employee data in localStorage
          localStorage.setItem('loggeed-in-employee', JSON.stringify(employee));
          this.loggedInEmployee.set(employee);
          return employee;
        }

        console.log('👤 ❌ No employee found for user_id:', userId);
        return null;
      }),
      catchError(err => {
        console.error('👤 🚨 Employee API error:', err.status, err.message);
        return of(null);
      })
    );
  }

  async setPOSProfile(employee: Employee) {
    return await lastValueFrom(this._posProfileService.getPOSProfileForEmployeeInBranch(employee))
  }
}
