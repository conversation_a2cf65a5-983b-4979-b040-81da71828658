import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Customer, FrappeListResponse } from '@models';
import { BaseHttpService } from '@core';

@Injectable({ providedIn: 'root' })
export class CustomerService extends BaseHttpService<Customer> {
  private readonly _baseUrl = '';
  private readonly _endpoint = 'api/resource/Customer';

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
  }

  getCustomersByGroup(): Observable<Customer[]> {
    const params = new HttpParams()
      .set(
        'or_filters',
        JSON.stringify([
          ['customer_group', '=', 'EGN Walk In Customer'],
          ['customer_group', '=', 'EGN Wholesale Customers']
        ])
      )
      .set(
        'fields',
        JSON.stringify([
          'customer_name',
          'customer_type',
          'customer_group',
          'territory',
          'default_price_list',
          'represents_company'
        ])
      );

    return this.http
      .get<FrappeListResponse<Customer>>(this.apiUrl, {
        params,
        headers: this.getHeaders(),
        withCredentials: true
      })
      .pipe(map(response => response.data));
  }
}