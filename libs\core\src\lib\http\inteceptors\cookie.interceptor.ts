import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject, isDevMode } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

export const cookieInterceptor: HttpInterceptorFn = (req, next) => {
  const cookieService = inject(CookieService);
  const router = inject(Router);

  return next(req.clone({
    withCredentials: true
  })).pipe(
    catchError((error: HttpErrorResponse) => {
      if (isDevMode()) {
        console.error('HTTP Error:', error);
      }

      // Handle 403 Forbidden - session expired or invalid
      if (error.status === 403) {
        console.log('🚨 Cookie Interceptor: 403 error detected');
        console.log('🚨 Request URL:', req.url);
        console.log('🚨 Error details:', error);

        // Only clear auth data if this is NOT during the login process
        // Check if the request is to the login endpoint
        if (!req.url.includes('/api/method/login') && !req.url.includes('/api/resource/Employee')) {
          console.log('🚨 Clearing auth data due to 403 on non-login request');

          // Clear all auth data
          localStorage.clear();
          sessionStorage.clear();

          // Clear ERPNext cookies
          const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];
          cookieNames.forEach(name => {
            cookieService.delete(name, '/');
            cookieService.delete(name);
          });

          router.navigate(['/auth/login']);
        } else {
          console.log('🚨 403 error during login/employee lookup - not clearing auth data');
        }
      }

      return throwError(() => error);
    })
  );
};
